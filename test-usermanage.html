<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理测试页面</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        // 简化版的图标组件
        const Search = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
            </svg>
        );

        const Plus = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
        );

        const Download = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7,10 12,15 17,10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
        );

        const Edit = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
        );

        const Trash2 = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="3,6 5,6 21,6"></polyline>
                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
            </svg>
        );

        const Eye = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
            </svg>
        );

        const User = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
            </svg>
        );

        const Save = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                <polyline points="17,21 17,13 7,13 7,21"></polyline>
                <polyline points="7,3 7,8 15,8"></polyline>
            </svg>
        );

        const AlertTriangle = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                <line x1="12" y1="9" x2="12" y2="13"></line>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
        );

        // 测试用户管理组件
        function TestUserManage() {
            const [darkMode, setDarkMode] = useState(false);

            return (
                <div className={`min-h-screen ${darkMode ? 'bg-gray-900' : 'bg-gray-100'}`}>
                    <div className="container mx-auto px-4 py-8">
                        <div className="mb-4">
                            <button
                                onClick={() => setDarkMode(!darkMode)}
                                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                            >
                                切换 {darkMode ? '浅色' : '深色'} 模式
                            </button>
                        </div>
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                            <h1 className="text-2xl font-bold mb-6">用户管理功能测试</h1>
                            <div className="space-y-4">
                                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                                    <h3 className="font-semibold text-green-800">✅ 已完成的功能</h3>
                                    <ul className="mt-2 text-sm text-green-700 space-y-1">
                                        <li>• 用户列表展示和分页</li>
                                        <li>• 搜索和筛选功能</li>
                                        <li>• 新增用户功能（包含表单验证）</li>
                                        <li>• 编辑用户功能（包含密码重置）</li>
                                        <li>• 删除用户功能</li>
                                        <li>• 查看用户详情</li>
                                        <li>• 数据导出为CSV</li>
                                        <li>• 角色权限自动分配</li>
                                        <li>• 深色模式支持</li>
                                        <li>• 响应式设计</li>
                                    </ul>
                                </div>
                                
                                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <h3 className="font-semibold text-blue-800">🔧 主要功能说明</h3>
                                    <ul className="mt-2 text-sm text-blue-700 space-y-1">
                                        <li>• <strong>表单验证</strong>：姓名、用户名、邮箱、电话格式验证</li>
                                        <li>• <strong>用户名唯一性</strong>：防止重复用户名</li>
                                        <li>• <strong>密码管理</strong>：新增时必填，编辑时可选重置</li>
                                        <li>• <strong>权限管理</strong>：根据角色自动分配对应权限</li>

                                        <li>• <strong>数据导出</strong>：导出用户数据为CSV格式</li>
                                    </ul>
                                </div>

                                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <h3 className="font-semibold text-yellow-800">📝 使用说明</h3>
                                    <ul className="mt-2 text-sm text-yellow-700 space-y-1">
                                        <li>• 点击"新增用户"按钮添加新用户</li>
                                        <li>• 使用搜索框和筛选器快速查找用户</li>
                                        <li>• 点击操作列的图标进行查看、编辑、删除操作</li>

                                        <li>• 点击"导出"按钮下载用户数据</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<TestUserManage />, document.getElementById('root'));
    </script>
</body>
</html>
